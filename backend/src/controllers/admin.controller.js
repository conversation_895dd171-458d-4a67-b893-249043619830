const { Admin } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class AdminController {
  /**
   * Get all admins with pagination and search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAdmins(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { page = 1, limit = 10, search = '' } = req.query;
      const offset = (page - 1) * limit;

      // Build search conditions
      const whereConditions = {};
      if (search) {
        whereConditions[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } }
        ];
      }

      // Get admins with pagination
      const { count, rows: admins } = await Admin.findAndCountAll({
        where: whereConditions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['created_at', 'DESC']],
        attributes: { exclude: ['password'] }
      });

      const totalPages = Math.ceil(count / limit);

      loggerWithId.info(`Retrieved ${admins.length} admins (page ${page}/${totalPages})`);

      res.json({
        success: true,
        data: admins,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Get admins error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve admins'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Get admin by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getAdmin(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;

      const admin = await Admin.findByPk(id, {
        attributes: { exclude: ['password'] }
      });

      if (!admin) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'ADMIN_NOT_FOUND',
            message: 'Admin not found'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      loggerWithId.info(`Retrieved admin: ${admin.email}`);

      res.json({
        success: true,
        data: admin,
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Get admin error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to retrieve admin'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Update admin
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updateAdmin(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;
      const { name, email, role, isActive } = req.body;

      const admin = await Admin.findByPk(id);

      if (!admin) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'ADMIN_NOT_FOUND',
            message: 'Admin not found'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Check if email is already taken by another admin
      if (email && email !== admin.email) {
        const existingAdmin = await Admin.findOne({
          where: { email, id: { [Op.ne]: id } }
        });
        if (existingAdmin) {
          return res.status(400).json({
            success: false,
            error: {
              code: 'EMAIL_ALREADY_EXISTS',
              message: 'Email is already taken by another admin'
            },
            timestamp: new Date().toISOString(),
            correlationId
          });
        }
      }

      // Only super admin can change role to super_admin
      if (role === 'super_admin' && req.user.role !== 'super_admin') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Only super admin can assign super admin role'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Update admin
      const updateData = {};
      if (name) updateData.name = name;
      if (email) updateData.email = email;
      if (role) updateData.role = role;
      if (typeof isActive === 'boolean') updateData.isActive = isActive;

      await admin.update(updateData);

      // Remove password from response
      const { password, ...adminWithoutPassword } = admin.toJSON();

      loggerWithId.info(`Updated admin: ${admin.email}`);

      res.json({
        success: true,
        data: adminWithoutPassword,
        message: 'Admin updated successfully',
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Update admin error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update admin'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }

  /**
   * Delete admin
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deleteAdmin(req, res) {
    const correlationId = uuidv4();
    const loggerWithId = logger.addCorrelationId(correlationId);
    
    try {
      const { id } = req.params;

      const admin = await Admin.findByPk(id);

      if (!admin) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'ADMIN_NOT_FOUND',
            message: 'Admin not found'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Prevent deleting self
      if (admin.id === req.user.id) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'CANNOT_DELETE_SELF',
            message: 'Cannot delete your own admin account'
          },
          timestamp: new Date().toISOString(),
          correlationId
        });
      }

      // Check if this is the last super admin
      if (admin.role === 'super_admin') {
        const superAdminCount = await Admin.count({
          where: { role: 'super_admin', isActive: true }
        });
        if (superAdminCount <= 1) {
          return res.status(400).json({
            success: false,
            error: {
              code: 'CANNOT_DELETE_LAST_SUPER_ADMIN',
              message: 'Cannot delete the last super admin account'
            },
            timestamp: new Date().toISOString(),
            correlationId
          });
        }
      }

      await admin.destroy();

      loggerWithId.info(`Deleted admin: ${admin.email}`);

      res.json({
        success: true,
        message: 'Admin deleted successfully',
        timestamp: new Date().toISOString(),
        correlationId
      });
    } catch (error) {
      loggerWithId.error('Delete admin error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete admin'
        },
        timestamp: new Date().toISOString(),
        correlationId
      });
    }
  }
}

module.exports = new AdminController(); 
const express = require('express');
const router = express.Router();
const adminController = require('../controllers/admin.controller');
const authMiddleware = require('../middleware/auth.middleware');
const { validatePagination, validateIdParam } = require('../middleware/validation.middleware');
const { CacheMiddleware } = require('../middleware/cache.middleware');

const cacheMiddleware = new CacheMiddleware();

/**
 * @swagger
 * tags:
 *   name: Admin Management
 *   description: Admin user management
 */

/**
 * @swagger
 * /api/admins:
 *   get:
 *     summary: Get all admins (Super Admin only)
 *     tags: [Admin Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *     responses:
 *       200:
 *         description: List of admins
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Admin'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 */
router.get('/', 
  authMiddleware.authenticateToken, 
  authMiddleware.requireSuperAdmin, 
  validatePagination, 
  cacheMiddleware.cacheResponse({ ttl: 300, isPrivate: true }),
  adminController.getAdmins
);

/**
 * @swagger
 * /api/admins/{id}:
 *   get:
 *     summary: Get admin by ID (Super Admin only)
 *     tags: [Admin Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     responses:
 *       200:
 *         description: Admin details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Admin'
 */
router.get('/:id', 
  authMiddleware.authenticateToken, 
  authMiddleware.requireSuperAdmin, 
  validateIdParam, 
  cacheMiddleware.cacheResponse({ ttl: 300, isPrivate: true }),
  adminController.getAdmin
);

/**
 * @swagger
 * /api/admins/{id}:
 *   put:
 *     summary: Update admin (Super Admin only)
 *     tags: [Admin Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 255
 *               email:
 *                 type: string
 *                 format: email
 *               role:
 *                 type: string
 *                 enum: [admin, super_admin]
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Admin updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.put('/:id', 
  authMiddleware.authenticateToken, 
  authMiddleware.requireSuperAdmin, 
  validateIdParam, 
  adminController.updateAdmin
);

/**
 * @swagger
 * /api/admins/{id}:
 *   delete:
 *     summary: Delete admin (Super Admin only)
 *     tags: [Admin Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID
 *     responses:
 *       200:
 *         description: Admin deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
router.delete('/:id', 
  authMiddleware.authenticateToken, 
  authMiddleware.requireSuperAdmin, 
  validateIdParam, 
  adminController.deleteAdmin
);

module.exports = router; 
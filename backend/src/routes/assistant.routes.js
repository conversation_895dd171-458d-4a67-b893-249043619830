const express = require('express');
const { body, param, query } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const rateLimitMiddleware = require('../middleware/rate-limit.middleware');
const assistantController = require('../controllers/assistant.controller');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// Apply rate limiting to all routes
router.use(rateLimitMiddleware.createRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
}));

/**
 * @swagger
 * /api/assistant/conversation/initialize:
 *   post:
 *     summary: Initialize conversation with OpenAI Assistant
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - trainerId
 *             properties:
 *               trainerId:
 *                 type: integer
 *                 description: ID of the trainer
 *               submoduleId:
 *                 type: integer
 *                 description: ID of the submodule (optional, uses first submodule if not provided)
 *     responses:
 *       200:
 *         description: Conversation initialized successfully
 *       400:
 *         description: Validation error or trainer not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/conversation/initialize',
  roleMiddleware.requireRole(['member']),
  [
    body('trainerId')
      .isInt({ min: 1 })
      .withMessage('Trainer ID must be a positive integer'),
    body('submoduleId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Submodule ID must be a positive integer')
  ],
  assistantController.initializeConversation
);

/**
 * @swagger
 * /api/assistant/message/send:
 *   post:
 *     summary: Send message to OpenAI Assistant
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - threadId
 *               - message
 *             properties:
 *               threadId:
 *                 type: integer
 *                 description: ID of the thread
 *               message:
 *                 type: string
 *                 description: Message content
 *                 minLength: 1
 *                 maxLength: 4000
 *     responses:
 *       200:
 *         description: Message sent successfully
 *       400:
 *         description: Validation error or thread not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/message/send',
  roleMiddleware.requireRole(['member']),
  rateLimitMiddleware.createRateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 20, // limit each IP to 20 messages per minute
    message: 'Too many messages sent, please slow down.'
  }),
  [
    body('threadId')
      .isInt({ min: 1 })
      .withMessage('Thread ID must be a positive integer'),
    body('message')
      .isString()
      .trim()
      .isLength({ min: 1, max: 4000 })
      .withMessage('Message must be between 1 and 4000 characters')
  ],
  assistantController.sendMessage
);

/**
 * @swagger
 * /api/assistant/submodule/switch:
 *   post:
 *     summary: Switch to different submodule
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentThreadId
 *               - newSubmoduleId
 *             properties:
 *               currentThreadId:
 *                 type: integer
 *                 description: ID of the current thread
 *               newSubmoduleId:
 *                 type: integer
 *                 description: ID of the new submodule
 *     responses:
 *       200:
 *         description: Submodule switched successfully
 *       400:
 *         description: Validation error or thread/submodule not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/submodule/switch',
  roleMiddleware.requireRole(['member']),
  [
    body('currentThreadId')
      .isInt({ min: 1 })
      .withMessage('Current thread ID must be a positive integer'),
    body('newSubmoduleId')
      .isInt({ min: 1 })
      .withMessage('New submodule ID must be a positive integer')
  ],
  assistantController.switchSubmodule
);

/**
 * @swagger
 * /api/assistant/conversation/history:
 *   get:
 *     summary: Get conversation history for member
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: trainerId
 *         schema:
 *           type: integer
 *         description: Filter by trainer ID (optional)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 500
 *           default: 100
 *         description: Maximum number of messages to return
 *     responses:
 *       200:
 *         description: Conversation history retrieved successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/conversation/history',
  roleMiddleware.requireRole(['member']),
  [
    query('trainerId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Trainer ID must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 500 })
      .withMessage('Limit must be between 1 and 500')
  ],
  assistantController.getConversationHistory
);

/**
 * @swagger
 * /api/assistant/progress/tracking:
 *   get:
 *     summary: Get progress tracking for member
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: trainerId
 *         schema:
 *           type: integer
 *         description: Filter by trainer ID (optional)
 *     responses:
 *       200:
 *         description: Progress tracking retrieved successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/progress/tracking',
  roleMiddleware.requireRole(['member']),
  [
    query('trainerId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Trainer ID must be a positive integer')
  ],
  assistantController.getProgressTracking
);

/**
 * @swagger
 * /api/assistant/thread/{threadId}/stats:
 *   get:
 *     summary: Get thread statistics
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: threadId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Thread ID
 *     responses:
 *       200:
 *         description: Thread statistics retrieved successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Thread not found
 *       500:
 *         description: Internal server error
 */
router.get('/thread/:threadId/stats',
  roleMiddleware.requireRole(['member']),
  [
    param('threadId')
      .isInt({ min: 1 })
      .withMessage('Thread ID must be a positive integer')
  ],
  assistantController.getThreadStats
);

/**
 * @swagger
 * /api/assistant/threads/active:
 *   get:
 *     summary: Get member's active threads
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: trainerId
 *         schema:
 *           type: integer
 *         description: Filter by trainer ID (optional)
 *     responses:
 *       200:
 *         description: Active threads retrieved successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/threads/active',
  roleMiddleware.requireRole(['member']),
  [
    query('trainerId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Trainer ID must be a positive integer')
  ],
  assistantController.getActiveThreads
);

/**
 * @swagger
 * /api/assistant/thread/{threadId}/archive:
 *   put:
 *     summary: Archive a thread
 *     tags: [Assistant]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: threadId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Thread ID
 *     responses:
 *       200:
 *         description: Thread archived successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Thread not found
 *       500:
 *         description: Internal server error
 */
router.put('/thread/:threadId/archive',
  roleMiddleware.requireRole(['member']),
  [
    param('threadId')
      .isInt({ min: 1 })
      .withMessage('Thread ID must be a positive integer')
  ],
  assistantController.archiveThread
);

module.exports = router;
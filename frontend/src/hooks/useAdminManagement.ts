'use client';

import { useState, useEffect } from 'react';
import { Admin } from '@/types';
import { api } from '@/lib/api';

export const useAdminManagement = () => {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(false);

  const [adminModal, setAdminModal] = useState({
    isOpen: false,
    admin: null as Admin | null,
    isEdit: false,
  });

  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    admin: null as Admin | null,
  });

  const [adminForm, setAdminForm] = useState({
    name: '',
    email: '',
    password: '',
    role: 'admin' as 'admin' | 'super_admin',
  });

  const fetchAdmins = async () => {
    try {
      setLoading(true);
      const response = await api.admins.getAll();
      setAdmins(response.data.data || response.data);
    } catch (error) {
      console.error('Error fetching admins:', error);
      // Fallback to mock data if API fails
      const mockAdmins: Admin[] = [
        {
          id: 1,
          name: '<PERSON>',
          email: '<EMAIL>',
          role: 'super_admin',
          isActive: true,
          lastLoginAt: '2024-01-15T10:30:00Z',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'admin',
          isActive: true,
          lastLoginAt: '2024-01-14T15:45:00Z',
          created_at: '2024-01-05T00:00:00Z',
          updated_at: '2024-01-14T15:45:00Z'
        }
      ];
      setAdmins(mockAdmins);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAdmins();
  }, []);

  const handleCreateAdmin = async () => {
    try {
      console.log('Creating admin:', adminForm);
      await api.admins.create(adminForm);
      await fetchAdmins();
      closeAdminModal();
    } catch (error) {
      console.error('Error creating admin:', error);
      throw error;
    }
  };

  const handleUpdateAdmin = async () => {
    if (!adminModal.admin) return;
    try {
      console.log('Updating admin:', adminModal.admin.id, adminForm);
      await api.admins.update(adminModal.admin.id, adminForm);
      await fetchAdmins();
      closeAdminModal();
    } catch (error) {
      console.error('Error updating admin:', error);
      throw error;
    }
  };

  const handleDeleteAdmin = async () => {
    if (!deleteModal.admin) return;
    try {
      console.log('Deleting admin:', deleteModal.admin.id);
      await api.admins.delete(deleteModal.admin.id);
      await fetchAdmins();
      closeDeleteModal();
    } catch (error) {
      console.error('Error deleting admin:', error);
      throw error;
    }
  };

  const openAdminModal = (admin: Admin | null, isEdit: boolean) => {
    if (isEdit && admin) {
      setAdminForm({
        name: admin.name,
        email: admin.email,
        password: '',
        role: admin.role,
      });
    } else {
      setAdminForm({ name: '', email: '', password: '', role: 'admin' });
    }
    setAdminModal({ isOpen: true, admin, isEdit });
  };

  const closeAdminModal = () => setAdminModal({ isOpen: false, admin: null, isEdit: false });
  const openDeleteModal = (admin: Admin) => setDeleteModal({ isOpen: true, admin });
  const closeDeleteModal = () => setDeleteModal({ isOpen: false, admin: null });

  return {
    admins,
    loading,
    adminModal,
    deleteModal,
    adminForm,
    setAdminForm,
    handleCreateAdmin,
    handleUpdateAdmin,
    handleDeleteAdmin,
    openAdminModal,
    closeAdminModal,
    openDeleteModal,
    closeDeleteModal,
  };
};
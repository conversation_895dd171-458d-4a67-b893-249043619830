import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor untuk menambahkan token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor untuk handle errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API functions
export const api = {
  // Auth
  auth: {
    login: (credentials: { email: string; password: string; userType: string }) =>
      apiClient.post('/auth/login', credentials),
    logout: () => apiClient.post('/auth/logout'),
    getProfile: () => apiClient.get('/auth/profile'),
    registerAdmin: (data: { name: string; email: string; password: string; role: string }) =>
      apiClient.post('/auth/register/admin', data),
    registerMember: (data: { name: string; email: string; password: string }) =>
      apiClient.post('/auth/register/member', data),
  },

  // Admins
  admins: {
    getAll: (params?: { page?: number; limit?: number; search?: string }) =>
      apiClient.get('/admins', { params }),
    getById: (id: number) => apiClient.get(`/admins/${id}`),
    create: (data: { name: string; email: string; password: string; role: string }) =>
      apiClient.post('/auth/register/admin', data),
    update: (id: number, data: Partial<{ name: string; email: string; role: string; isActive: boolean }>) =>
      apiClient.put(`/admins/${id}`, data),
    delete: (id: number) => apiClient.delete(`/admins/${id}`),
  },

  // Trainers
  trainers: {
    getAll: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>
      apiClient.get('/trainers', { params }),
    getById: (id: number) => apiClient.get(`/trainers/${id}`),
    create: (data: { name: string; systemPrompt: string; description?: string; status?: string }) =>
      apiClient.post('/trainers', data),
    update: (id: number, data: Partial<{ name: string; systemPrompt: string; description: string; status: string }>) =>
      apiClient.put(`/trainers/${id}`, data),
    delete: (id: number) => apiClient.delete(`/trainers/${id}`),
    assign: (id: number, memberIds: number[]) =>
      apiClient.post(`/trainers/${id}/assign`, { memberIds }),
    unassign: (id: number, memberId: number) =>
      apiClient.delete(`/trainers/${id}/assign/${memberId}`),
    getStatistics: (id: number) => apiClient.get(`/trainers/${id}/statistics`),
  },

  // Trainer Submodules
  submodules: {
    getByTrainer: (trainerId: number) =>
      apiClient.get(`/trainers/${trainerId}/submodules`),
    create: (trainerId: number, data: { name: string; systemPrompt: string; description?: string; status?: string }) =>
      apiClient.post(`/trainers/${trainerId}/submodules`, data),
    getById: (id: number) => apiClient.get(`/submodules/${id}`),
    update: (id: number, data: Partial<{ name: string; systemPrompt: string; description: string; status: string }>) =>
      apiClient.put(`/submodules/${id}`, data),
    delete: (id: number) => apiClient.delete(`/submodules/${id}`),
    reorder: (trainerId: number, submoduleIds: number[]) =>
      apiClient.put(`/trainers/${trainerId}/submodules/reorder`, { submoduleIds }),
    validatePrompt: (systemPrompt: string) =>
      apiClient.post('/submodules/validate-prompt', { systemPrompt }),
  },

  // Members
  members: {
    getAll: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>
      apiClient.get('/members', { params }),
    getById: (id: number) => apiClient.get(`/members/${id}`),
    create: (data: { name: string; email: string; password: string }) =>
      apiClient.post('/members', data),
    update: (id: number, data: Partial<{ name: string; email: string; isActive: boolean }>) =>
      apiClient.put(`/members/${id}`, data),
    delete: (id: number) => apiClient.delete(`/members/${id}`),
    getAssignedTrainers: (memberId: number) =>
      apiClient.get(`/members/${memberId}/trainers`),
  },

  // Monitoring
  monitoring: {
    getDashboard: () => apiClient.get('/monitoring/dashboard'),
    getSuperAdminDashboard: () => apiClient.get('/monitoring/super-admin/dashboard'),
    getConversations: (params?: { trainerId?: number; memberId?: number; dateFrom?: string; dateTo?: string }) =>
      apiClient.get('/monitoring/conversations', { params }),
    getStatistics: () => apiClient.get('/monitoring/statistics'),
  },

  // Progress
  progress: {
    getByMember: (memberId: number) => apiClient.get(`/progress/member/${memberId}`),
    getByTrainer: (trainerId: number) => apiClient.get(`/progress/trainer/${trainerId}`),
    getTrends: (memberId: number, timeRange: string) =>
      apiClient.get(`/progress/member/${memberId}/trends`, { params: { timeRange } }),
    generateReport: (filters: {
      trainerId?: number;
      memberId?: number;
      dateFrom?: string;
      dateTo?: string;
    }) => apiClient.post('/progress/report', filters),
  },

  // Chat
  chat: {
    initialize: (trainerId: number) =>
      apiClient.post(`/chat/${trainerId}/initialize`),
    sendMessage: (trainerId: number, data: { message: string; sessionId: string }) =>
      apiClient.post(`/chat/${trainerId}/message`, data),
    getHistory: (trainerId: number, params?: { page?: number; limit?: number }) =>
      apiClient.get(`/chat/${trainerId}/history`, { params }),
  },

  // System
  system: {
    getHealth: () => apiClient.get('/system/health'),
    getCacheStatus: () => apiClient.get('/system/cache/status'),
    flushCache: () => apiClient.post('/system/cache/flush'),
  },

  // Convenience methods for easier usage
  getTrainers: (params?: { page?: number; limit?: number; search?: string; status?: string }) =>
    apiClient.get('/trainers', { params }),
  deleteTrainer: (id: number) => apiClient.delete(`/trainers/${id}`),
};

export default apiClient;
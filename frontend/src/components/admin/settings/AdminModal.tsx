'use client';

import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';

interface AdminModalProps {
  isOpen: boolean;
  isEdit: boolean;
  onClose: () => void;
  formState: {
    name: string;
    email: string;
    password: string;
    role: 'admin' | 'super_admin';
  };
  onFormChange: (field: string, value: string) => void;
  onSubmit: () => Promise<void>;
}

const AdminModal: React.FC<AdminModalProps> = ({
  isOpen,
  isEdit,
  onClose,
  formState,
  onFormChange,
  onSubmit,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');
      await onSubmit();
    } catch (err: any) {
      setError(err.response?.data?.error?.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Edit Admin' : 'Add Admin'}
      size="md"
    >
      <div className="space-y-4">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}
        <Input
          label="Name"
          value={formState.name}
          onChange={(e) => onFormChange('name', e.target.value)}
          placeholder="Enter admin name"
        />
        
        <Input
          label="Email"
          type="email"
          value={formState.email}
          onChange={(e) => onFormChange('email', e.target.value)}
          placeholder="Enter admin email"
        />
        
        {!isEdit && (
          <Input
            label="Password"
            type="password"
            value={formState.password}
            onChange={(e) => onFormChange('password', e.target.value)}
            placeholder="Enter password"
          />
        )}
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
          <select
            value={formState.role}
            onChange={(e) => onFormChange('role', e.target.value as 'admin' | 'super_admin')}
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="admin">Admin</option>
            <option value="super_admin">Super Admin</option>
          </select>
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} loading={loading}>
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default AdminModal;